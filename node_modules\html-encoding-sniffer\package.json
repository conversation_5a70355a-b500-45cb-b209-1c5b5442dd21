{"name": "html-encoding-sniffer", "description": "Sniff the encoding from a HTML byte stream", "keywords": ["encoding", "html"], "version": "3.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/html-encoding-sniffer", "main": "lib/html-encoding-sniffer.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint ."}, "dependencies": {"whatwg-encoding": "^2.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "eslint": "^7.32.0", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}}