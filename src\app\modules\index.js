// ✅ CONSOLIDATED: playerEvents.js and appEvents.js merged into AppEventHandlers.js
export { WebSocketManager } from './WebSocketManager.js';
export { GameUIManager } from './GameUIManager.js';
export { MapRenderer } from './MapRenderer.js';
export { PlayerController } from './PlayerController.js';
export { PerformanceMonitor } from './PerformanceMonitor.js';
export { NotificationManager } from './NotificationManager.js';
export { ChatManager } from './ChatManager.js';
export { GameStateManager } from './GameStateManager.js';
export { AppEventHandlers } from './AppEventHandlers.js';
// ✅ CONSOLIDATED: handleGameEvent is now a method in AppEventHandlers class
