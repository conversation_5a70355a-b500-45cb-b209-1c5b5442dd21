* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #2c3e50;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.nickname-screen {
  text-align: center;
  background-color: #34495e;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.nickname-screen h1 {
  font-size: 3em;
  margin-bottom: 30px;
  color: #e74c3c;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.nickname-form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

#nickname-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

#nickname-form label {
  font-size: 1.2em;
}

#nickname-form input {
  padding: 12px;
  font-size: 1.1em;
  border: none;
  border-radius: 5px;
  background-color: #ecf0f1;
  color: #2c3e50;
}

#nickname-form button {
  padding: 12px 24px;
  font-size: 1.1em;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

#nickname-form button:hover {
  background-color: #c0392b;
}

#nickname-form button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

.error {
  background-color: #e74c3c;
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  display: none;
}

/* ✅ ENHANCED: Modern lobby design with better visual hierarchy */
.waiting-screen {
  text-align: center;
  background: linear-gradient(135deg, #2c3e50, #34495e, #2c3e50);
  padding: 50px;
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  max-width: 900px;
  margin: 0 auto;
  border: 2px solid rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
}

/* ✅ NEW: Animated background pattern */
.waiting-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(155, 89, 182, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(46, 204, 113, 0.1) 0%, transparent 50%);
  animation: lobby-pulse 8s ease-in-out infinite;
  z-index: 0;
}

@keyframes lobby-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* ✅ ENHANCED: Better layout with improved spacing and visual hierarchy */
.waiting-content {
  display: flex;
  gap: 50px;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

/* ✅ ENHANCED: Modern title with gradient and animation */
.waiting-screen h1 {
  background: linear-gradient(135deg, #3498db, #9b59b6, #e74c3c);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 40px;
  font-size: 3em;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: gradient-shift 6s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* ✅ ENHANCED: Better player info section with modern styling */
.player-info {
  flex: 1;
  min-width: 250px;
  background: rgba(44, 62, 80, 0.6);
  padding: 30px;
  border-radius: 15px;
  border: 1px solid rgba(52, 152, 219, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.player-info p {
  font-size: 1.4em;
  margin-bottom: 25px;
  color: #ecf0f1;
  font-weight: 500;
}

/* ✅ ENHANCED: Modern player list with better visual feedback */
.players-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 25px;
}

.player-item {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  padding: 18px 20px;
  border-radius: 12px;
  font-size: 1.2em;
  font-weight: 500;
  color: #ecf0f1;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.player-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.3), transparent);
  transition: left 0.5s ease;
}

.player-item:hover {
  border-color: #3498db;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.player-item:hover::before {
  left: 100%;
}

/* ✅ NEW: Current player highlighting */
.player-item.current-player {
  border: 2px solid #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.player-item .player-name {
  font-weight: bold;
  color: #ecf0f1;
  display: block;
  margin-bottom: 4px;
}

.player-item .player-status {
  font-size: 0.9em;
  color: #2ecc71;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ✅ ENHANCED: Modern timer with pulsing animation and better visibility */
.waiting-timer {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  padding: 25px 30px;
  border-radius: 15px;
  margin-top: 30px;
  border: 3px solid #d35400;
  box-shadow:
    0 8px 25px rgba(243, 156, 18, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: timer-pulse 2s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes timer-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(243, 156, 18, 0.6);
  }
}

.waiting-timer h3 {
  font-size: 1.8em;
  margin-bottom: 10px;
  color: white;
}

.waiting-timer p {
  font-size: 1em;
  color: white;
  margin: 0;
}

/* ✅ ENHANCED: Dramatic countdown with modern styling */
.countdown {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  padding: 35px;
  border-radius: 20px;
  margin-top: 30px;
  border: 3px solid #a93226;
  box-shadow:
    0 12px 40px rgba(231, 76, 60, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: countdown-container-pulse 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.countdown::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: countdown-shine 3s ease-in-out infinite;
}

@keyframes countdown-container-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 12px 40px rgba(231, 76, 60, 0.5);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 16px 50px rgba(231, 76, 60, 0.7);
  }
}

@keyframes countdown-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.countdown h2 {
  font-size: 3.5em;
  margin-bottom: 15px;
  color: white;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
  animation: countdown-number-pulse 1s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes countdown-number-pulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.1);
    filter: brightness(1.3);
  }
}

.countdown p {
  font-size: 1.4em;
  color: white;
  font-weight: 500;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 1;
}

/* Chat Styles */
.chat-container {
  flex: 1;
  min-width: 300px;
  text-align: left;
  background-color: #2c3e50;
  border-radius: 8px;
  padding: 20px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-container h3 {
  margin: 0 0 15px 0;
  color: #3498db;
  text-align: center;
}

.chat-messages {
  flex: 1;
  max-height: 250px;
  overflow-y: auto;
  background-color: #1a1a1a;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #34495e;
}

.chat-message {
  margin-bottom: 8px;
  word-wrap: break-word;
}

.chat-nickname {
  font-weight: bold;
  color: #3498db;
  margin-right: 5px;
}

.chat-text {
  color: #ecf0f1;
}

.chat-input-container {
  display: flex;
  gap: 10px;
}

.chat-input-container input {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: #ecf0f1;
  color: #2c3e50;
  font-size: 14px;
}

.chat-input-container button {
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.chat-input-container button:hover {
  background-color: #2980b9;
}

.chat-input-container button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

/* Game Screen Chat Styles */
.game-chat-container {
  width: 280px;
  background-color: #2c3e50;
  border-radius: 8px;
  padding: 15px;
  height: 500px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.game-chat-container h4 {
  margin: 0 0 10px 0;
  color: #3498db;
  text-align: center;
  font-size: 16px;
}

.game-chat-messages {
  flex: 1;
  overflow-y: auto;
  background-color: #1a1a1a;
  border-radius: 5px;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #34495e;
  font-size: 13px;
}

.game-chat-input-container {
  display: flex;
  gap: 8px;
}

.game-chat-input-container input {
  flex: 1;
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  background-color: #ecf0f1;
  color: #2c3e50;
  font-size: 13px;
}

.game-chat-input-container button {
  padding: 6px 12px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.3s;
}

.game-chat-input-container button:hover {
  background-color: #2980b9;
}

.game-chat-input-container button:disabled {
  background-color: #7f8c8d;
  cursor: not-allowed;
}

.game-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  max-width: none;
  width: auto;
}

.game-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
}

.game-ui {
  display: flex;
  gap: 30px;
  background-color: #2c3e50;
  padding: 15px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
  min-width: 280px;
}

.player-stats {
  display: flex;
  gap: 20px;
}

.player-stats span {
  font-weight: bold;
  font-size: 1.1em;
}

/* ✅ NEW: Game Players List Styling */
.game-players-list {
  background-color: #34495e;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #4a5f7a;
}

.game-players-list h4 {
  margin: 0 0 8px 0;
  color: #ecf0f1;
  font-size: 0.9em;
  text-align: center;
  border-bottom: 1px solid #4a5f7a;
  padding-bottom: 6px;
}

.players-list-game {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.game-player-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  margin-bottom: 4px;
  border-radius: 6px;
  color: #ecf0f1;
  font-size: 0.85em;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid transparent;
}

.game-player-item:not(.current-player) {
  background: linear-gradient(135deg, #34495e, #2c3e50);
}

.game-player-item.current-player {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: 1px solid #5dade2;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.player-name-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.player-crown {
  font-size: 1.1em;
  filter: drop-shadow(0 0 3px gold);
}

.player-name {
  font-weight: bold;
}

.player-lives-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.player-lives-container span:first-child {
  font-size: 0.8em;
  color: #bdc3c7;
}

.player-lives-count {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 0.85em;
  min-width: 16px;
  text-align: center;
}

.controls-info {
  font-size: 0.9em;
  color: #bdc3c7;
}

.game-map {
  display: grid;
  grid-template-columns: repeat(13, 45px);
  grid-template-rows: repeat(13, 45px);
  gap: 2px;
  background-color: #1a1a1a;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.cell {
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: relative;
  border-radius: 3px;
  transition: all 0.1s ease;
}

.cell.empty {
  background-color: #27ae60;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ✅ NEW: Spawn area styling for better visual distinction */
.cell.spawn {
  background-color: #95a5a6;
  background-image:
    linear-gradient(45deg, #bdc3c7 25%, transparent 25%),
    linear-gradient(-45deg, #bdc3c7 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #bdc3c7 75%),
    linear-gradient(-45deg, transparent 75%, #bdc3c7 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
  border: 2px solid #7f8c8d;
}

.cell.spawn-safe {
  background-color: #27ae60;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #95a5a6;
}

.cell.wall {
  background-color: #7f8c8d;
  background-image: linear-gradient(45deg, #95a5a6 25%, transparent 25%), 
                    linear-gradient(-45deg, #95a5a6 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #95a5a6 75%), 
                    linear-gradient(-45deg, transparent 75%, #95a5a6 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cell.block {
  background-color: #d35400;
  background-image: linear-gradient(45deg, #e67e22 25%, transparent 25%), 
                    linear-gradient(-45deg, #e67e22 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #e67e22 75%), 
                    linear-gradient(-45deg, transparent 75%, #e67e22 75%);
  background-size: 6px 6px;
  background-position: 0 0, 0 3px, 3px -3px, -3px 0px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bomb {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  font-size: 24px;
  z-index: 5;
  animation: bomb-pulse 0.5s infinite alternate;

  /* ✅ PERFORMANCE: Hardware acceleration for smooth animations */
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

@keyframes bomb-pulse {
  0% { transform: translate3d(-50%, -50%, 0) scale(1); }
  100% { transform: translate3d(-50%, -50%, 0) scale(1.1); }
}

.player {
  position: absolute;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  /* ✅ PERFORMANCE: Hardware acceleration for smooth movement */
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  transform: translate3d(0, 0, 0);
  transition: transform 0.1s ease;

  /* Hide text content when using images */
  font-size: 0;
  color: transparent;
}

.player:hover {
  transform: translate3d(0, 0, 0) scale(1.05);
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* ✅ NEW: Character sprite backgrounds */
.player.player-0 {
  background-image: url('../../assets/chara_aka.png');
  /* Fallback for image loading issues */
  background-color: #e74c3c;
}

.player.player-1 {
  background-image: url('../../assets/chara_shiro.png');
  /* Fallback for image loading issues */
  background-color: #3498db;
}

.player.player-2 {
  background-image: url('../../assets/chara_pink.png');
  /* Fallback for image loading issues */
  background-color: #f39c12;
}

.player.player-3 {
  background-image: url('../../assets/chara_kuro.png');
  /* Fallback for image loading issues */
  background-color: #9b59b6;
}



/* Performance display styling */
#performance-display {
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive design */
@media (max-width: 800px) {
  .game-map {
    grid-template-columns: repeat(13, 35px);
    grid-template-rows: repeat(13, 35px);
    gap: 1px;
    padding: 10px;
  }
  
  .cell {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }
  
  .player {
    width: 28px;
    height: 28px;
    font-size: 12px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    background-size: cover;
    background-position: center;
  }
  
  .bomb {
    font-size: 18px;
  }
}

@media (max-width: 1000px) {
  .game-content {
    flex-direction: column;
    align-items: center;
  }

  .game-chat-container {
    width: 100%;
    max-width: 600px;
    height: 200px;
    order: 2;
  }

  .game-map {
    order: 1;
  }
}

/* ✅ NEW: Loading states and transitions */
.lobby-loading {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  color: #3498db;
  font-weight: 500;
}

.lobby-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #3498db;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ✅ ENHANCED: Better responsive design */
@media (max-width: 800px) {
  .waiting-screen {
    padding: 30px 20px;
    margin: 10px;
  }

  .waiting-screen h1 {
    font-size: 2.5em;
  }

  .waiting-content {
    flex-direction: column;
    gap: 25px;
  }

  .player-info {
    min-width: auto;
    padding: 20px;
  }

  .chat-container {
    min-width: auto;
    max-height: 300px;
  }

  .game-chat-container {
    height: 150px;
  }
}

@media (max-width: 600px) {
  .nickname-screen {
    padding: 30px 20px;
    margin: 20px;
  }

  .nickname-screen h1 {
    font-size: 2.5em;
  }

  #nickname-form input {
    font-size: 1.1em;
    padding: 12px 15px;
  }

  #nickname-form button {
    font-size: 1.1em;
    padding: 12px 25px;
  }

  .waiting-screen {
    padding: 20px 15px;
  }

  .chat-container {
    max-height: 250px;
  }

  .chat-messages {
    max-height: 150px;
  }

  .game-map {
    grid-template-columns: repeat(13, 28px);
    grid-template-rows: repeat(13, 28px);
  }

  .cell {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .player {
    width: 22px;
    height: 22px;
    font-size: 10px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    background-size: cover;
    background-position: center;
  }

  .bomb {
    font-size: 16px;
  }
}

/* ✅ ENHANCED: Compact Professional Game End Screen Design */
.game-end-screen {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
  text-align: center;
  max-width: 700px;
  width: 95%;
  margin: 0 auto;
  animation: slideInUp 0.6s ease-out;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-end-screen h1 {
  font-size: 2.5em;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #e74c3c, #f39c12);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: bold;
  flex-shrink: 0;
}

.winner-announcement {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  padding: 15px;
  border-radius: 12px;
  margin: 15px 0;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  border: 2px solid #f1c40f;
  flex-shrink: 0;
}

.winner-announcement h2 {
  font-size: 1.8em;
  margin-bottom: 8px;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.winner-announcement .winner-name {
  font-size: 1.5em;
  font-weight: bold;
  color: #f1c40f;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  margin-bottom: 8px;
}

/* ✅ ENHANCED: Personalized game end screen styling */
.winner-announcement.victory-player {
  animation: victoryPulse 1.5s ease-in-out infinite alternate;
  transform: scale(1.05);
  border: 3px solid #f1c40f;
}

.winner-announcement.defeat-player {
  animation: defeatFade 3s ease-in-out infinite alternate;
  transform: scale(0.95);
  border: 2px solid rgba(231, 76, 60, 0.5);
}

.personal-outcome {
  font-size: 2.8em !important;
  font-weight: bold;
  margin: 10px 0;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  animation: textGlow 2s ease-in-out infinite alternate;
}

.personal-submessage {
  font-size: 1.6em;
  font-weight: bold;
  margin: 15px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: #ecf0f1;
}

.victory-flavor {
  font-size: 1.1em;
  margin-top: 15px;
  font-style: italic;
  color: #f1c40f;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.defeat-flavor {
  font-size: 1.1em;
  margin-top: 15px;
  font-style: italic;
  color: #ecf0f1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.neutral-flavor {
  font-size: 1.1em;
  margin-top: 15px;
  font-style: italic;
  color: #bdc3c7;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Victory animation */
@keyframes victoryPulse {
  0% {
    box-shadow: 0 0 20px rgba(46, 204, 113, 0.5);
  }
  100% {
    box-shadow: 0 0 40px rgba(46, 204, 113, 0.8), 0 0 60px rgba(241, 196, 15, 0.4);
  }
}

/* Defeat animation */
@keyframes defeatFade {
  0% {
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(231, 76, 60, 0.3);
  }
  100% {
    opacity: 1;
    box-shadow: 0 0 25px rgba(231, 76, 60, 0.5);
  }
}

/* Text glow animation */
@keyframes textGlow {
  0% {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  }
  100% {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7), 0 0 20px rgba(255, 255, 255, 0.3);
  }
}

/* ✅ GHOST MODE: Styling for eliminated players */
.player-ghost {
  opacity: 0.4 !important;
  filter: grayscale(100%) brightness(0.7) !important;
  pointer-events: none !important;
  position: relative;
  z-index: 1; /* Lower than alive players */
}

.player-ghost::after {
  content: '👻';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: ghostFloat 2s ease-in-out infinite alternate;
}

/* Ghost floating animation */
@keyframes ghostFloat {
  0% {
    transform: translateY(0px);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-3px);
    opacity: 0.9;
  }
}

/* Ensure alive players are above ghost players */
.player:not(.player-ghost) {
  z-index: 2;
}

.final-results {
  background: rgba(52, 73, 94, 0.8);
  padding: 15px;
  border-radius: 12px;
  margin: 15px 0;
  border: 2px solid #3498db;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.final-results h3 {
  font-size: 1.5em;
  margin-bottom: 15px;
  color: #3498db;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  flex-shrink: 0;
}

.players-final-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 10px;
  overflow-y: auto;
  max-height: 300px;
  padding-right: 5px;
  flex: 1;
}

/* Custom scrollbar for player stats */
.players-final-stats::-webkit-scrollbar {
  width: 6px;
}

.players-final-stats::-webkit-scrollbar-track {
  background: rgba(52, 73, 94, 0.3);
  border-radius: 3px;
}

.players-final-stats::-webkit-scrollbar-thumb {
  background: #3498db;
  border-radius: 3px;
}

.players-final-stats::-webkit-scrollbar-thumb:hover {
  background: #2980b9;
}

.player-final-stat {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  padding: 12px;
  border-radius: 10px;
  border: 2px solid #7f8c8d;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.player-final-stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.player-final-stat.current-player {
  border-color: #3498db;
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.player-final-stat.winner {
  border-color: #f1c40f;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  box-shadow: 0 4px 8px rgba(241, 196, 15, 0.4);
}

.player-name {
  display: block;
  font-size: 1.1em;
  font-weight: bold;
  margin-bottom: 6px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.player-lives {
  display: block;
  font-size: 1em;
  margin-bottom: 6px;
  color: #ecf0f1;
}

.player-status {
  display: block;
  font-size: 0.9em;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-status.alive {
  background-color: #27ae60;
  color: white;
}

.player-status.eliminated {
  background-color: #e74c3c;
  color: white;
}

/* Additional styling for countdown */
.countdown-to-lobby {
  margin-top: 15px;
  padding: 10px;
  background: rgba(52, 73, 94, 0.6);
  border-radius: 8px;
  border: 1px solid #7f8c8d;
}

.countdown-to-lobby p {
  margin: 0;
  font-size: 0.9em;
  color: #ecf0f1;
}

#lobby-countdown {
  font-weight: bold;
  color: #f39c12;
  font-size: 1em;
}

.game-end-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 15px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.game-end-actions button {
  padding: 10px 20px;
  font-size: 1em;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 140px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.game-end-actions button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.game-end-actions .primary-button {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.game-end-actions .primary-button:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
}

.game-end-actions .secondary-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.game-end-actions .secondary-button:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
}

/* Responsive Design for Game End Screen */
@media (max-width: 768px) {
  .game-end-screen {
    padding: 15px;
    margin: 5px;
    max-height: 95vh;
  }

  .game-end-screen h1 {
    font-size: 2em;
    margin-bottom: 10px;
  }

  .winner-announcement {
    padding: 12px;
    margin: 10px 0;
  }

  .winner-announcement h2 {
    font-size: 1.5em;
    margin-bottom: 6px;
  }

  .winner-announcement .winner-name {
    font-size: 1.2em;
    margin-bottom: 6px;
  }

  .final-results {
    padding: 12px;
    margin: 10px 0;
  }

  .final-results h3 {
    font-size: 1.3em;
    margin-bottom: 12px;
  }

  .players-final-stats {
    grid-template-columns: 1fr;
    gap: 8px;
    max-height: 200px;
  }

  .player-final-stat {
    padding: 10px;
    min-height: 70px;
  }

  .game-end-actions {
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
  }

  .game-end-actions button {
    width: 100%;
    max-width: 250px;
    padding: 8px 16px;
    font-size: 0.9em;
    min-width: auto;
  }

  .countdown-to-lobby {
    margin-top: 10px;
    padding: 8px;
  }

  .countdown-to-lobby p {
    font-size: 0.8em;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .game-end-screen {
    padding: 10px;
    margin: 2px;
  }

  .game-end-screen h1 {
    font-size: 1.8em;
  }

  .winner-announcement h2 {
    font-size: 1.3em;
  }

  .winner-announcement .winner-name {
    font-size: 1.1em;
  }

  .players-final-stats {
    max-height: 150px;
  }

  .player-final-stat {
    min-height: 60px;
    padding: 8px;
  }

  .player-name {
    font-size: 1em;
  }

  .player-lives {
    font-size: 0.9em;
  }

  .player-status {
    font-size: 0.8em;
    padding: 3px 6px;
  }
}

/* ✅ ENHANCED: Power-up styling and animations */
.powerup {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  font-size: 1.8em;
  z-index: 5;
  animation: powerupFloat 3s ease-in-out infinite;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;

  /* ✅ ENHANCED: Glowing border effect */
  border: 2px solid transparent;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent 70%);

  /* ✅ PERFORMANCE: Hardware acceleration for smooth animations */
  will-change: transform, opacity, box-shadow;
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

.powerup:hover {
  transform: translate3d(-50%, -50%, 0) scale(1.2);
  filter: brightness(1.3);
}

/* ✅ ENHANCED: Specific styling for each power-up type with distinct animations */
.powerup-bombs {
  color: #e74c3c;
  text-shadow:
    0 0 15px rgba(231, 76, 60, 0.9),
    0 0 30px rgba(231, 76, 60, 0.6),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  border-color: rgba(231, 76, 60, 0.6);
  box-shadow:
    0 0 20px rgba(231, 76, 60, 0.4),
    inset 0 0 20px rgba(231, 76, 60, 0.1);
  animation: powerupFloat 3s ease-in-out infinite, bombPowerupGlow 2s ease-in-out infinite alternate;
}

.powerup-flames {
  color: #f39c12;
  text-shadow:
    0 0 15px rgba(243, 156, 18, 0.9),
    0 0 30px rgba(243, 156, 18, 0.6),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  border-color: rgba(243, 156, 18, 0.6);
  box-shadow:
    0 0 20px rgba(243, 156, 18, 0.4),
    inset 0 0 20px rgba(243, 156, 18, 0.1);
  animation: powerupFloat 3s ease-in-out infinite, flamePowerupFlicker 1.5s ease-in-out infinite;
}

.powerup-speed {
  color: #3498db;
  text-shadow:
    0 0 15px rgba(52, 152, 219, 0.9),
    0 0 30px rgba(52, 152, 219, 0.6),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  border-color: rgba(52, 152, 219, 0.6);
  box-shadow:
    0 0 20px rgba(52, 152, 219, 0.4),
    inset 0 0 20px rgba(52, 152, 219, 0.1);
  animation: powerupFloat 3s ease-in-out infinite, speedPowerupZoom 2.5s ease-in-out infinite;
}

/* ✅ ENHANCED: Power-up animations with distinct visual effects */
@keyframes powerupFloat {
  0%, 100% {
    transform: translate3d(-50%, -50%, 0) translateY(0px) scale(1);
    opacity: 0.9;
  }
  33% {
    transform: translate3d(-50%, -50%, 0) translateY(-3px) scale(1.05);
    opacity: 1;
  }
  66% {
    transform: translate3d(-50%, -50%, 0) translateY(2px) scale(0.98);
    opacity: 0.95;
  }
}

/* ✅ BOMB POWER-UP: Pulsing glow animation */
@keyframes bombPowerupGlow {
  0% {
    box-shadow:
      0 0 20px rgba(231, 76, 60, 0.4),
      inset 0 0 20px rgba(231, 76, 60, 0.1);
  }
  100% {
    box-shadow:
      0 0 35px rgba(231, 76, 60, 0.7),
      inset 0 0 25px rgba(231, 76, 60, 0.2);
  }
}

/* ✅ FLAME POWER-UP: Flickering flame animation */
@keyframes flamePowerupFlicker {
  0%, 100% {
    text-shadow:
      0 0 15px rgba(243, 156, 18, 0.9),
      0 0 30px rgba(243, 156, 18, 0.6),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  25% {
    text-shadow:
      0 0 20px rgba(255, 165, 0, 1),
      0 0 40px rgba(255, 69, 0, 0.8),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  50% {
    text-shadow:
      0 0 18px rgba(255, 140, 0, 0.95),
      0 0 35px rgba(255, 99, 71, 0.7),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
  75% {
    text-shadow:
      0 0 22px rgba(255, 215, 0, 1),
      0 0 45px rgba(255, 165, 0, 0.9),
      2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

/* ✅ SPEED POWER-UP: Zoom/dash animation */
@keyframes speedPowerupZoom {
  0%, 100% {
    transform: translate3d(-50%, -50%, 0) scale(1);
  }
  10% {
    transform: translate3d(-50%, -50%, 0) scale(1.15);
  }
  20% {
    transform: translate3d(-50%, -50%, 0) scale(0.9);
  }
  30% {
    transform: translate3d(-50%, -50%, 0) scale(1.05);
  }
  40% {
    transform: translate3d(-50%, -50%, 0) scale(1);
  }
}

@keyframes powerupCollected {
  0% {
    transform: translate3d(-50%, -50%, 0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate3d(-50%, -50%, 0) scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: translate3d(-50%, -50%, 0) scale(0.5);
    opacity: 0;
  }
}

.powerup.collected {
  animation: powerupCollected 0.5s ease-out forwards;
}

/* ✅ POWER-UP SPAWN ANIMATION */
@keyframes powerupSpawn {
  0% {
    transform: translate3d(-50%, -50%, 0) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate3d(-50%, -50%, 0) scale(1.3);
    opacity: 0.8;
  }
  100% {
    transform: translate3d(-50%, -50%, 0) scale(1);
    opacity: 1;
  }
}

/* ✅ FIXED: Missing bomb explosion animation keyframes */
@keyframes bomberman-explosion-pop {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
    filter: brightness(2) saturate(1.5);
  }
  15% {
    transform: scale(0.4) rotate(3deg);
    opacity: 0.9;
    filter: brightness(1.8) saturate(1.4);
  }
  30% {
    transform: scale(1.1) rotate(-1deg);
    opacity: 1;
    filter: brightness(1.5) saturate(1.3);
  }
  50% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    filter: brightness(1.2) saturate(1.1);
  }
  75% {
    transform: scale(0.9) rotate(1deg);
    opacity: 0.8;
    filter: brightness(1) saturate(0.9);
  }
  90% {
    transform: scale(0.6) rotate(0deg);
    opacity: 0.4;
    filter: brightness(0.8) saturate(0.7);
  }
  100% {
    transform: scale(0.3) rotate(0deg);
    opacity: 0;
    filter: brightness(0.6) saturate(0.5);
  }
}

@keyframes explosion-fade-out {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1.1) saturate(1);
  }
  60% {
    opacity: 0.5;
    transform: scale(0.7);
    filter: brightness(0.8) saturate(0.8);
  }
  100% {
    opacity: 0;
    transform: scale(0.4);
    filter: brightness(0.6) saturate(0.6);
  }
}

.explosion-fade-out {
  animation: explosion-fade-out 0.3s ease-out forwards;
}

.powerup.spawning {
  animation: powerupSpawn 0.6s ease-out;
}

/* ✅ ENHANCED: Additional explosion effect classes */
.explosion-effect.center {
  animation: bomberman-explosion-pop 0.8s ease-out forwards;
  font-size: 1.8em;
  text-shadow:
    0 0 15px rgba(255, 107, 53, 1),
    0 0 25px rgba(255, 69, 0, 0.8),
    0 0 35px rgba(255, 140, 0, 0.6);
}

.explosion-effect.flame {
  animation: bomberman-explosion-pop 0.7s ease-out forwards;
  color: #ff4500;
  text-shadow:
    0 0 8px rgba(255, 69, 0, 0.9),
    0 0 16px rgba(255, 140, 0, 0.7),
    0 0 24px rgba(255, 165, 0, 0.5);
}

/* ✅ ENHANCED: Particle-like explosion effects */
@keyframes explosionParticle {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  40% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.9;
  }
  80% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.4);
    opacity: 0;
  }
}

.explosion-particle {
  animation: explosionParticle 0.6s ease-out forwards;
}

/* ✅ RESPONSIVE: Smaller power-ups on mobile */
@media (max-width: 768px) {
  .powerup {
    font-size: 1.5em;
  }
}

@media (max-width: 480px) {
  .powerup {
    font-size: 1.2em;
  }
}

/* ✅ ENHANCED: Advanced Bomb Explosion Animation System */
.explosion-effect {
  font-size: 1.5em;
  opacity: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;

  /* ✅ ENHANCED: Smooth explosion visual effects */
  color: #ff6b35;
  text-shadow:
    0 0 10px rgba(255, 107, 53, 0.8),
    0 0 20px rgba(255, 69, 0, 0.6),
    0 0 30px rgba(255, 140, 0, 0.4);

  /* ✅ PERFORMANCE: Hardware acceleration */
  will-change: transform, opacity, filter;
  backface-visibility: hidden;

  /* ✅ SMOOTH: Transition for dynamic effects */
  transition: all 0.1s ease-out;
}

/* Player item styles */
.player-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  margin-bottom: 5px;
  border-radius: 6px;
  color: #ecf0f1;
  font-size: 0.8em;
  transition: all 0.3s ease;
}

.player-item.current-player {
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: 2px solid #3498db;
}

.player-item:not(.current-player) {
  background: linear-gradient(135deg, #34495e, #2c3e50);
  border: 1px solid #2c3e50;
}

.player-name {
  font-weight: bold;
}

.player-status {
  font-size: 0.7em;
  color: #bdc3c7;
}


