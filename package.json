{"name": "todo-custom-framework", "version": "1.0.0", "description": "Custom Framework TodoMVC Implementation", "type": "module", "main": "server.cjs", "scripts": {"start": "node server.cjs", "dev": "npx http-server . -p 3000 -c-1", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["todo", "framework", "javascript"], "author": "", "license": "ISC", "devDependencies": {"http-server": "^14.1.1"}, "dependencies": {"ws": "^8.18.3"}}